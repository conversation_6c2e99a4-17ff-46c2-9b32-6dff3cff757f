<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('pay_periods', function (Blueprint $table) {
            $table->id();
            $table->string('period_name');
            $table->integer('fiscal_year');
            $table->date('start_date');
            $table->date('finish_date');
            $table->string('pay_period_type');
            $table->string('pay_period_frequency');
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('pay_periods');
    }
}; 