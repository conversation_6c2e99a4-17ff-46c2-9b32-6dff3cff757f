import { defineStore } from 'pinia';
import useCommonStore from '@spa/stores/modules/commonStore.js';
import { ref } from 'vue';
import apiClient from '@spa/services/api.client.js';

export const useFinancialYearStore = defineStore('financialYear', () => {
    const storeUrl = ref('v2/tenant/pay-period/financial-years');

    const {
        serverPagination,
        filters,
        loading,
        enableLoader,
        all,
        form_rows,
        formRef,
        formDialog,
        formData,
        errors,
        statusOptions,
        selected,
        getAll,
        onRequest,
        store,
        update,
        remove,
        createFunction,
        closeFunction,
        edit,
        submitFormData,
        confirmDelete,
        fetchPaged,
        clearFunction,
        toggleStatus,
        notifySuccess,
        notifyError,
        changeStatusOtherColumn,
    } = useCommonStore(storeUrl.value);

    // Transform API response to match AsyncSelect format
    const transformData = (data) => {
        if (!data) return [];
        return Object.entries(data).map(([value, label]) => ({
            fiscal_year: value,
            fiscal_year_label: label
        }));
    };

    // Override the getAll method to transform data
    const getAllFinancialYears = async () => {
        try {
            const response = await apiClient.get(`api/${storeUrl.value}`);
            all.value = transformData(response.data);
            return all.value;
        } catch (error) {
            notifyError('Failed to fetch financial years');
            throw error;
        }
    };

    return {
        all,
        loading,
        errors,
        formData,
        formDialog,
        formRef,
        form_rows,
        statusOptions,
        selected,
        getAll: getAllFinancialYears,
        onRequest,
        store,
        update,
        remove,
        createFunction,
        closeFunction,
        edit,
        submitFormData,
        confirmDelete,
        fetchPaged,
        clearFunction,
        toggleStatus,
        notifySuccess,
        notifyError,
        changeStatusOtherColumn,
    };
});
