<template>
    <AsyncGrid
        :columns="columns"
        :store="store"
        :show-refresh-button="true"
        :show-filter-button="true"
        :add-permissions="null"
        :enableSelection="true"
        :has-create-action="false"
        :has-export="false"
        :gridStyle="{
            height: '100%',
        }"
        @handel-reset="
            () => {
                initFilters();
                store.selected = [];
            }
        "
    >
        <!-- Generate Pay Period Form -->
        <template #top>
            <div class="bg-white rounded-lg shadow p-6 mb-4">
                <h3 class="text-lg font-medium mb-4">Generate Pay Period Dates</h3>
                <form @submit.prevent="generatePayPeriod" class="space-y-4">
                    <div class="grid grid-cols-2 gap-4">
                        <div class="form-group">
                            <label class="block text-sm font-medium text-gray-700 mb-1">Pay Period Type</label>
                            <select v-model="formData.pay_period_type" class="form-control">
                                <option v-for="(label, value) in payPeriodTypes" :key="value" :value="value">
                                    {{ label }}
                                </option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="block text-sm font-medium text-gray-700 mb-1">Pay Period Frequency</label>
                            <select v-model="formData.pay_period_frequency" class="form-control">
                                <option v-for="(label, value) in payPeriodFrequencies" :key="value" :value="value">
                                    {{ label }}
                                </option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="block text-sm font-medium text-gray-700 mb-1">Start Date From</label>
                            <input type="date" v-model="formData.start_date_from" class="form-control" />
                        </div>
                        <div class="form-group">
                            <label class="block text-sm font-medium text-gray-700 mb-1">Start Date To</label>
                            <input type="date" v-model="formData.start_date_to" class="form-control" />
                        </div>
                    </div>
                    <div class="flex justify-end">
                        <button type="submit" class="btn btn-primary" :disabled="loading">
                            {{ loading ? 'Generating...' : 'Generate Pay Period' }}
                        </button>
                    </div>
                </form>
            </div>
        </template>

        <template #filters>
            <FilterBlockWrapper label="Financial Year">
                <FinancialYearSelect
                    v-model="filters.financial_year"
                    @update:modelValue="loadPayPeriods"
                />
            </FilterBlockWrapper>
        </template>

        <template #body-cell-period_name="{ props }">
            {{ props.dataItem.period_name }}
        </template>
        <template #body-cell-fiscal_year="{ props }">
            {{ props.dataItem.fiscal_year }}
        </template>
        <template #body-cell-start_date="{ props }">
            {{ props.dataItem.start_date }}
        </template>
        <template #body-cell-finish_date="{ props }">
            {{ props.dataItem.finish_date }}
        </template>
    </AsyncGrid>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { usePayPeriodStore } from '@spa/stores/modules/payPeriod/payPeriodStore';
import AsyncGrid from '@spa/components/AsyncComponents/Grid/AsyncGrid.vue';
import FilterBlockWrapper from '@spa/components/AsyncComponents/Grid/Partials/FilterBlockWrapper.vue';
import FinancialYearSelect from './FinancialYearSelect.vue';

const store = usePayPeriodStore();

const formData = ref({
    pay_period_type: '',
    pay_period_frequency: '',
    start_date_from: '',
    start_date_to: ''
});

const filters = ref({
    financial_year: new Date().getFullYear()
});

const loading = ref(false);
const payPeriods = ref([]);

// These would come from your store/API
const payPeriodTypes = ref({});
const payPeriodFrequencies = ref({});
const fiscalYears = ref([]);

const columns = [
    {
        name: 'period_name',
        title: 'Period Name',
        field: 'period_name',
        sortable: true,
    },
    {
        name: 'fiscal_year',
        title: 'Fiscal Year',
        field: 'fiscal_year',
        sortable: true,
    },
    {
        name: 'start_date',
        title: 'Start Date',
        field: 'start_date',
        sortable: true,
    },
    {
        name: 'finish_date',
        title: 'Finish Date',
        field: 'finish_date',
        sortable: true,
    }
];

const generatePayPeriod = async () => {
    loading.value = true;
    try {
        await store.generatePayPeriod(formData.value);
        await loadPayPeriods();
    } catch (error) {
        console.error('Error generating pay period:', error);
    } finally {
        loading.value = false;
    }
};

const loadPayPeriods = async () => {
    try {
        const response = await store.getPayPeriods(filters.value);
        payPeriods.value = response.data;
    } catch (error) {
        console.error('Error loading pay periods:', error);
    }
};

const initFilters = () => {
    filters.value = {
        financial_year: new Date().getFullYear()
    };
};

onMounted(async () => {
    // Load initial data
    await Promise.all([
        store.getPayPeriodTypes().then(types => payPeriodTypes.value = types),
        store.getPayPeriodFrequencies().then(freqs => payPeriodFrequencies.value = freqs),
        store.getFiscalYears().then(years => fiscalYears.value = years)
    ]);
    await loadPayPeriods();
});
</script>

<style scoped>
.form-control {
    @apply mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm;
}

.btn {
    @apply inline-flex justify-center rounded-md border border-transparent px-4 py-2 text-sm font-medium shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2;
}

.btn-primary {
    @apply bg-indigo-600 text-white hover:bg-indigo-700 focus:ring-indigo-500;
}

.btn-primary:disabled {
    @apply bg-indigo-400 cursor-not-allowed;
}
</style> 