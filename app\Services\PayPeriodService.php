<?php

namespace App\Services;

use App\Model\PayPeriod;
use Carbon\Carbon;
use Config;

class PayPeriodService
{
    public function generatePayPeriods(array $data)
    {
        $startDate = Carbon::parse($data['start_date_from']);
        $endDate = Carbon::parse($data['start_date_to']);
        $type = $data['pay_period_type'];
        $frequency = $data['pay_period_frequency'];

        $periods = [];
        $currentDate = $startDate;

        while ($currentDate <= $endDate) {
            $periodEnd = $this->calculatePeriodEnd($currentDate, $type, $frequency);
            
            if ($periodEnd > $endDate) {
                $periodEnd = $endDate;
            }

            $periods[] = PayPeriod::create([
                'period_name' => $this->generatePeriodName($currentDate, $periodEnd),
                'fiscal_year' => $this->getFiscalYear($currentDate),
                'start_date' => $currentDate->format('Y-m-d'),
                'finish_date' => $periodEnd->format('Y-m-d'),
                'pay_period_type' => $type,
                'pay_period_frequency' => $frequency
            ]);

            $currentDate = $periodEnd->addDay();
        }

        return $periods;
    }

    public function getPayPeriods(array $filters = [])
    {
        $query = PayPeriod::query();

        if (!empty($filters['financial_year'])) {
            $query->where('fiscal_year', $filters['financial_year']);
        }

        return $query->orderBy('start_date')->get();
    }

    public function getPayPeriodTypes()
    {
        return Config::get('constants.arrPayPeriodType', []);
    }

    public function getPayPeriodFrequencies()
    {
        return Config::get('constants.arrPayPeriodFrequency', []);
    }

    public function getFiscalYears()
    {
        $currentYear = Carbon::now()->year;
        $years = [];
        
        for ($i = $currentYear; $i > ($currentYear - 10); $i--) {
            $years[] = $i;
        }

        return $years;
    }

    protected function calculatePeriodEnd(Carbon $startDate, string $type, string $frequency)
    {
        switch ($type) {
            case 'weekly':
                return $startDate->copy()->addDays(6);
            case 'fortnightly':
                return $startDate->copy()->addDays(13);
            case 'monthly':
                return $startDate->copy()->endOfMonth();
            default:
                return $startDate->copy()->addDays(6);
        }
    }

    protected function generatePeriodName(Carbon $startDate, Carbon $endDate)
    {
        return sprintf(
            '%s to %s',
            $startDate->format('d M Y'),
            $endDate->format('d M Y')
        );
    }

    protected function getFiscalYear(Carbon $date)
    {
        $month = $date->month;
        $year = $date->year;

        // Assuming fiscal year starts in July
        if ($month >= 7) {
            return $year + 1;
        }

        return $year;
    }
} 