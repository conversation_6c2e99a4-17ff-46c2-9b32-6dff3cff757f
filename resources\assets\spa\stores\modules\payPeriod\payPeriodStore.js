import { defineStore } from 'pinia';
import useCommonStore from '@spa/stores/modules/commonStore.js';
import { ref } from 'vue';
import apiClient from '@spa/services/api.client.js';

export const usePayPeriodStore = defineStore('usePayPeriodStore', () => {
    const storeUrl = ref('v2/tenant/pay-periods');

    const {
        serverPagination,
        filters,
        loading,
        enableLoader,
        progresses,
        all,
        form_rows,
        formRef,
        formDialog,
        formData,
        errors,
        statusOptions,
        selected,
        getAll,
        onRequest,
        store,
        update,
        remove,
        createFunction,
        closeFunction,
        edit,
        submitFormData,
        confirmDelete,
        fetchPaged,
        clearFunction,
        toggleStatus,
        notifySuccess,
        notifyError,
        jsonToFormData,
        changeStatusOtherColumn,
    } = useCommonStore(storeUrl.value);

    const generatePayPeriod = async (payload) => {
        try {
            const response = await apiClient.post(
                'api/v2/tenant/pay-periods/generate',
                jsonToFormData(payload)
            );
            notifySuccess('Pay period generated successfully');
            return response;
        } catch (error) {
            notifyError('Failed to generate pay period');
            throw error;
        }
    };

    const getPayPeriods = async (filters) => {
        try {
            const response = await apiClient.get('api/v2/tenant/pay-periods', { params: filters });
            return response;
        } catch (error) {
            notifyError('Failed to fetch pay periods');
            throw error;
        }
    };

    const getPayPeriodTypes = async () => {
        try {
            const response = await apiClient.get('api/v2/tenant/pay-periods/types');
            return response.data;
        } catch (error) {
            notifyError('Failed to fetch pay period types');
            throw error;
        }
    };

    const getPayPeriodFrequencies = async () => {
        try {
            const response = await apiClient.get('api/v2/tenant/pay-periods/frequencies');
            return response.data;
        } catch (error) {
            notifyError('Failed to fetch pay period frequencies');
            throw error;
        }
    };

    const getFiscalYears = async () => {
        try {
            const response = await apiClient.get('api/v2/tenant/pay-periods/fiscal-years');
            return response.data;
        } catch (error) {
            notifyError('Failed to fetch fiscal years');
            throw error;
        }
    };

    return {
        // Common store methods
        serverPagination,
        filters,
        loading,
        enableLoader,
        progresses,
        all,
        form_rows,
        formRef,
        formDialog,
        formData,
        errors,
        statusOptions,
        selected,
        getAll,
        onRequest,
        store,
        update,
        remove,
        createFunction,
        closeFunction,
        edit,
        submitFormData,
        confirmDelete,
        fetchPaged,
        clearFunction,
        toggleStatus,
        notifySuccess,
        notifyError,
        jsonToFormData,
        changeStatusOtherColumn,

        // Pay period specific methods
        generatePayPeriod,
        getPayPeriods,
        getPayPeriodTypes,
        getPayPeriodFrequencies,
        getFiscalYears,
    };
});  