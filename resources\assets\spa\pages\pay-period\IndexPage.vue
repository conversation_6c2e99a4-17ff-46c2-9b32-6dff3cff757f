<script setup>
import PayPeriodListComponent from '@spa/modules/pay-period/PayPeriodListComponent.vue';
import Layout from '@spa/pages/Layouts/Layout';
import { Head } from '@inertiajs/vue3';
import PageTitleContent from '@spa/pages/Layouts/PageTitleContent.vue';
</script>

<template>
    <Layout :no-spacing="true">
        <Head title="Generate Pay Period" />
        <template v-slot:pageTitleContent>
            <PageTitleContent title="Generate Pay Period" :back="false" />
        </template>
        <div class="h-screen-header flex flex-col px-8 py-6">
            <PayPeriodListComponent />
        </div>
    </Layout>
</template>

<style scoped></style> 