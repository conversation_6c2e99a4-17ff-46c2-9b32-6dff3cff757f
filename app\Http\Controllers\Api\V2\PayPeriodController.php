<?php

namespace App\Http\Controllers\Api\V2;

use App\Http\Controllers\Controller;
use App\Services\PayPeriodService;
use Illuminate\Http\Request;

class PayPeriodController extends Controller
{
    protected $payPeriodService;

    public function __construct(PayPeriodService $payPeriodService)
    {
        $this->payPeriodService = $payPeriodService;
    }

    public function generate(Request $request)
    {
        $validated = $request->validate([
            'pay_period_type' => 'required|string',
            'pay_period_frequency' => 'required|string',
            'start_date_from' => 'required|date',
            'start_date_to' => 'required|date|after_or_equal:start_date_from',
        ]);

        $result = $this->payPeriodService->generatePayPeriods($validated);

        return response()->json([
            'success' => true,
            'message' => 'Pay periods generated successfully',
            'data' => $result
        ]);
    }

    public function index(Request $request)
    {
        $filters = $request->only(['financial_year']);
        $payPeriods = $this->payPeriodService->getPayPeriods($filters);

        return response()->json([
            'success' => true,
            'data' => $payPeriods
        ]);
    }

    public function getTypes()
    {
        $types = $this->payPeriodService->getPayPeriodTypes();
        return response()->json($types);
    }

    public function getFrequencies()
    {
        $frequencies = $this->payPeriodService->getPayPeriodFrequencies();
        return response()->json($frequencies);
    }

    public function getFiscalYears()
    {
        $years = $this->payPeriodService->getFiscalYears();
        return response()->json($years);
    }
} 