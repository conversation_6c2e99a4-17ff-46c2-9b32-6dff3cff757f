<?php

namespace GalaxyAPI\Controllers;

use App\Model\GeneratePayPeriod;
use App\Model\GeneratePayPeriodDetail;
use GalaxyAPI\Requests\EmptyRequest;
use GalaxyAPI\Resources\PayPeriodResource;

class PayPeriodController extends CrudBaseController
{
    public function __construct()
    {
        parent::__construct(
            model: GeneratePayPeriod::class,
            storeRequest: EmptyRequest::class,
            updateRequest: EmptyRequest::class,
            resource: PayPeriodResource::class,
        );
    }

    public function financialYears()
    {
        $collegeId = auth()->user()->college_id;
        
        $resultArr = GeneratePayPeriodDetail::where('college_id', $collegeId)
                                            ->groupBy('fiscal_year')
                                            ->pluck('fiscal_year', 'fiscal_year')
                                            ->toArray();

        return response()->json($resultArr);
    }
}
