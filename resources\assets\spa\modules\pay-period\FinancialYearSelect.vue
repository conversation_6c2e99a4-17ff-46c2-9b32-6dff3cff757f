<script setup>
import AsyncSelect from '@spa/components/AsyncComponents/Select/AsyncSelect.vue';
import { useFinancialYearStore } from '@spa/stores/modules/payPeriod/useFinancialYear';
import { computed, onMounted } from 'vue';

const props = defineProps({
    modelValue: [String, Number, Array, Object],
    label: String,
    className: String,
    optionValue: {
        type: String,
        default: 'fiscal_year',
    },
    optionLabel: {
        type: String,
        default: 'fiscal_year_label',
    },
    disabled: Boolean,
    clearable: {
        type: Boolean,
        default: false,
    },
    multiple: {
        type: Boolean,
        default: false,
    },
    readonly: Boolean,
    useChips: {
        type: Boolean,
        default: false,
    },
});

const emit = defineEmits(['update:modelValue']);
const vModel = computed({
    get() {
        return props.modelValue;
    },
    set(val) {
        emit('update:modelValue', val);
    },
});

const store = useFinancialYearStore();

onMounted(async () => {
    await store.getAll();
});
</script>

<template>
    <AsyncSelect
        :label="label"
        :className="className"
        :optionValue="optionValue"
        :optionLabel="optionLabel"
        :disabled="disabled"
        :store="store"
        v-model="vModel"
        :clearable="clearable"
        :multiple="multiple"
        :readonly="readonly"
        :useChips="useChips"
    />
</template>

<style scoped></style> 