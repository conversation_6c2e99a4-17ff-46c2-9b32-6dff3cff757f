<?php

namespace App\Model;

use Illuminate\Database\Eloquent\Model;

class PayPeriod extends Model
{
    protected $table = 'pay_periods';

    protected $fillable = [
        'period_name',
        'fiscal_year',
        'start_date',
        'finish_date',
        'pay_period_type',
        'pay_period_frequency'
    ];

    protected $casts = [
        'start_date' => 'date',
        'finish_date' => 'date',
    ];
} 