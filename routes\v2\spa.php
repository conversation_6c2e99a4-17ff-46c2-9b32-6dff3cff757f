<?php

use App\Http\Middleware\UserTypeBasedRequestModifier;
use App\Services\CommonService;
use Illuminate\Support\Facades\Route;
use Stancl\Tenancy\Middleware\InitializeTenancyByDomain;
use Stancl\Tenancy\Middleware\PreventAccessFromCentralDomains;

Route::group(['middleware' => ['mail', \App\Http\Middleware\HandleInertiaRequests::class, UserTypeBasedRequestModifier::class]], function () {

    Route::get('spa/ziggy', fn () => response()->json(new \Tightenco\Ziggy\Ziggy));
    Route::inertia('spa/kitchensink', 'kitchensink/KitchenSink');

    // Notification routes
    Route::get('spa/notifications', 'Spa\NotificationsController@index')->name('spa.notifications.index');
    Route::post('spa/notifications/mark-all-as-read', 'Spa\NotificationsController@markAllAsRead')->name('spa.notifications.mark-all-as-read');
    Route::post('spa/notifications/mark-selected-as-read', 'Spa\NotificationsController@markSelectedAsRead')->name('spa.notifications.mark-selected-as-read');
    Route::post('spa/notifications/{id}/read', 'Spa\NotificationsController@markAsReadAndRedirect')->name('spa.notifications.read');
    Route::post('spa/notifications/list', 'Spa\NotificationsController@list')->name('spa.notifications.list');

    Route::group(['middleware' => [
        'admin',
        PreventAccessFromCentralDomains::class,
    ]], function () {

        /* routes for attendance (Admin Attendance) */
        /* changed by Harish Chandra Kharel */
        Route::get('spa/attendance/batch', 'Spa\AttendanceViewController@viewAttendanceByBatch')->name('spa.attendance.viewattendancebybatch');
        Route::get('spa/attendance/student', 'Spa\AttendanceViewController@viewAttendanceByStudent')->name('spa.attendance.viewattendancebystudent');
        Route::get('spa/attendance/download/{timetableid}/pdf', 'Spa\AttendanceViewController@downloadAttendancePdf')->name('spa.attendance.downloadattendancepdf');
        Route::get('spa/attendance/download/{timetableid}/xls', 'Spa\AttendanceViewController@downloadAttendanceXls')->name('spa.attendance.downloadattendancexls');
        Route::post('spa/attendance/export', 'Spa\AttendanceViewController@exportBatchesAttendance')->name('spa.attendance.export');
        Route::post('spa/attendance/print', 'Spa\AttendanceViewController@printBatchesAttendance')->name('spa.attendance.print');
        Route::post('spa/student/email/send', 'Spa\AttendanceViewController@sendMailToStudent')->name('spa.student.sendemail');

        // Admin Attendance
        Route::get('spa/view-send-warning-letter', 'Spa\AttendanceViewController@viewSendWarningLetter')->name('spa.attendance.viewsendwarningletter');
        Route::post('spa/get-attendance-batch-grid-column', 'Spa\AttendanceViewController@getAttendanceByBatchGridColumn')->name('spa.attendance.getattendancebybatchgridcolumn');
        Route::post('spa/get-searched-students', 'Spa\AttendanceViewController@getSearchedStudents')->name('spa.attendance.getsearchedstudents');
        Route::post('spa/get-course-from-campus', 'Spa\AttendanceViewController@getCourseFromCampus')->name('spa.attendance.getcoursefromcampus');
        Route::post('spa/get-subject-from-course', 'Spa\AttendanceViewController@getSubjectFromCourse')->name('spa.attendance.getsubjectfromcourse');
        Route::post('spa/get-batch-from-subject', 'Spa\AttendanceViewController@getBatchFromSubject')->name('spa.attendance.getbatchfromsubject');
        Route::post('spa/get-batch-info', 'Spa\AttendanceViewController@getBatchInfo')->name('spa.attendance.getbatchinfo');
        Route::post('spa/get-student-from-batch', 'Spa\AttendanceViewController@getStudentFromBatch')->name('spa.attendance.getstudentfrombatch');
        Route::post('spa/get-student-info', 'Spa\AttendanceViewController@getStudentInfo')->name('spa.attendance.getstudentinfo');
        Route::post('spa/get-student-attendance-data', 'Spa\AttendanceViewController@getStudentAttendanceData')->name('spa.attendance.getstudentattendancedata');
        Route::post('spa/get-sms-template-detail', 'Spa\AttendanceViewController@getSmsTemplateDetail')->name('spa.attendance.getsmstemplatedetail');
        Route::post('spa/get-sms-template-content', 'Spa\AttendanceViewController@getSmsTemplateDetailData')->name('spa.attendance.getSmsTemplateDetailData');
        Route::post('spa/update-attendance-data', 'Spa\AttendanceViewController@updateAttendanceData')->name('spa.attendance.updateattendancedata');
        Route::post('spa/update-single-student-attendance', 'Spa\AttendanceViewController@updateSingleStudentAttendance')->name('spa.attendance.updatesinglestudentattendance');
        Route::post('spa/generate-letter-pdf', 'Spa\AttendanceViewController@generateLetterPdf')->name('spa.attendance.generateletterpdf');
        Route::post('spa/send-letter-in-mail-to-student', 'Spa\AttendanceViewController@sendLetterInMailToStudent')->name('spa.attendance.sendletterinmailtostudent');
        Route::post('spa/send-mail-to-student', 'Spa\AttendanceViewController@sendMailToStudent')->name('spa.attendance.sendmailtostudent');
        Route::post('spa/send-sms-to-student', 'Spa\AttendanceViewController@sendSmsToStudent')->name('spa.attendance.sendsmstostudent');
        Route::get('spa/download-letter-pdf/{filename}', 'Spa\AttendanceViewController@downloadLetterPdf')->name('spa.attendance.downloadletterpdf');

        Route::post('spa/generate-letter-pdf-with-watermark', 'Spa\AttendanceViewController@generateLetterPdfWithWatermark')->name('spa.attendance.generateletterpdfwithwatermark');
        Route::post('spa/download-letter-pdf-zip', 'Spa\AttendanceViewController@downloadLetterPdfZip')->name('spa.attendance.downloadletterpdfzip');

        Route::get('spa/courses', 'Spa\CoursesController@index')->name('spa.courses.index');

        /* Routes for course sync with moodle */
        Route::post('spa/courses/sync-to-moodle', 'Spa\CoursesController@courseSyncToMoodle')->name('spa.courses.synctomoodle');
        Route::post('spa/courses/sync-from-moodle', 'Spa\CoursesController@courseSyncFromMoodle')->name('spa.courses.syncfrommoodle');

        Route::get('spa/courses/add', 'Spa\CoursesController@add')->name('spa.courses.add');

        /* this method is not being used */
        // Route::get('spa/courses/add-course/{course}', 'Spa\CoursesController@addCourse')->name('spa.courses.addcourse');

        Route::get('spa/courses/add-details/{course}', 'Spa\CoursesController@addDetails')->name('spa.courses.detailadd');
        /*
        Route not being used
        Route::get('spa/courses/unit/{course}/{unit}', 'Spa\CoursesController@getUnitDetail')->name('spa.courses.getunitdata');
        */
        Route::get('spa/courses/unit/{course}/{unit}', 'Spa\CoursesController@syncUnitDetail')->name('spa.courses.syncunitdata');
        Route::get('spa/courses/sync/{code}', 'Spa\CoursesController@syncCourseInformation')->name('spa.courses.sync');
        Route::post('spa/courses/legacysync', 'Spa\CoursesController@forceSyncLegacyData')->name('spa.courses.forcesync');

        /* course profile */
        Route::get('spa/courses/profile/{course}', 'Spa\CoursesController@courseProfile')->name('spa.courses.profile');

        /* routes to save form data */
        Route::post('spa/courses/general/save', 'Spa\CoursesController@saveCourseGeneralDetails')->name('spa.courses.savegeneral');
        Route::put('spa/courses/hours-fees/save', 'Spa\CoursesController@saveHoursFeesDetails')->name('spa.courses.savehoursfees');
        Route::put('spa/courses/faculty/save', 'Spa\CoursesController@saveFacultyDetails')->name('spa.courses.savefaculty');
        Route::put('spa/courses/avetmiss/save', 'Spa\CoursesController@saveAvetmissDetails')->name('spa.courses.saveavetmiss');
        /* routes to update the status of particular course */
        Route::post('spa/courses/status', 'Spa\CoursesController@updateCourseStatus')->name('spa.courses.status');
        /* route to update course packaging */
        Route::post('spa/courses/packaging', 'Spa\CoursesController@updatePackage')->name('spa.courses.packaging');
        /* route to add multiple units from the training.gov.au and is applicable only for vet courses */
        Route::post('spa/courses/units/add', 'Spa\CoursesController@addUnits')->name('spa.courses.addunits');
        /* route to add single new unit (can add custom unit) */
        Route::post('spa/courses/unit/add', 'Spa\CoursesController@addCustomUnit')->name('spa.courses.addunit');
        /* route to load units for the template */
        Route::get('spa/courses/{code}/units/template/{template?}', 'Spa\CoursesController@loadTemplateUnits')->name('spa.courses.loadtemplateunits');
        /* route to update single new unit (can update custom unit) */
        Route::put('spa/courses/update-unit', 'Spa\CoursesController@updateUnit')->name('spa.courses.saveunit');
        /* route to assign single unit to its solo subject */
        Route::post('spa/courses/assign-subject', 'Spa\CoursesController@assignSoloSubject')->name('spa.courses.assignunit');
        /* route to add multiple units from the training.gov.au and is applicable only for vet courses */
        Route::post('spa/courses/units/sort', 'Spa\CoursesController@sortUnits')->name('spa.courses.orderunits');

        Route::post('spa/courses/subject/add', 'Spa\CoursesController@saveCourseSubject')->name('spa.courses.addsubject');
        Route::post('spa/courses/subject/competencyelements', 'Spa\CoursesController@getSubjectCompetencyElements')->name('spa.courses.loadcompetencyelements');
        Route::post('spa/courses/subject/competencyelements/save', 'Spa\CoursesController@saveSubjectCompetencyElements')->name('spa.courses.savecompetencyelements');
        Route::post('spa/courses/subject/competencyelements/remove', 'Spa\CoursesController@deleteSubjectCompetencyElements')->name('spa.courses.removecompetencyelements');
        Route::post('spa/courses/shortcourse/intake/save', 'Spa\CoursesController@saveShortCourseIntake')->name('spa.courses.saveshortcourseintake');
        Route::get('spa/courses/{code}/intakes', 'Spa\CoursesController@getShortCourseIntakes')->name('spa.courses.getshortcourseintakes');
        Route::post('spa/courses/{code}/intakes/{action}', 'Spa\CoursesController@processShortCourseIntakesActions')->name('spa.courses.intakesactions');

        Route::get('spa/courses/template/copyunits', 'Spa\CourseTemplateController@loadUnitsToCopy')->name('spa.courses.copytemplateunits');
        Route::post('spa/courses/template/copyunits', 'Spa\CourseTemplateController@copyUnitsToTemplate')->name('spa.courses.savecopiedtemplateunit');
        Route::post('spa/courses/template/copysubjects', 'Spa\CourseTemplateController@copySubjectsToTemplate')->name('spa.courses.savecopiedtemplatesubjects');
        Route::post('spa/courses/subjects/search', 'Spa\CoursesController@searchSubjects')->name('spa.courses.searchsubjects');
        Route::post('spa/courses/subjects/add', 'Spa\CoursesController@saveCourseSubjects')->name('spa.courses.savecoursesubjects');

        Route::put('spa/courses/highered/save', 'Spa\CoursesController@saveHigheredInfo')->name('spa.courses.savehighered');
        Route::post('spa/courses/subject/delete', 'Spa\CoursesController@destroySubject')->name('spa.courses.deletesubject');
        Route::post('spa/courses/unit/delete', 'Spa\CoursesController@destroyUnit')->name('spa.courses.deleteunit');
        /* routes to delete particular course */
        Route::delete('spa/courses/delete/{id}', 'Spa\CoursesController@destroyCourse')->name('spa.courses.destroy');

        Route::post('spa/courses/quickadd', 'Spa\CoursesController@quickadd')->name('spa.courses.quickadd');
        Route::get('spa/courses/units', 'Spa\CoursesController@searchUnits')->name('spa.courses.units');
        Route::get('spa/course-types', 'Spa\CoursesController@types')->name('spa.courses.types');
        Route::get('spa/course-templates', 'Spa\CoursesController@templates')->name('spa.courses.templates');
        Route::delete('spa/course-templates/{id}', 'Spa\CoursesController@destroyTemplate')->name('spa.courses.templates.destroy');
        Route::post('spa/course-templates/duplicate/{id}', 'Spa\CoursesController@duplicateTemplate')->name('spa.courses.templates.duplicate');

        /* routes for course intakes */
        Route::get('spa/courses/intakes', 'Spa\CoursesController@intakes')->name('spa.courses.intakes');
        Route::get('spa/courses/intake/{id}', 'Spa\CoursesController@getIntakeDetail')->name('spa.intakes.detail');
        Route::get('spa/courses/intakes/{id}', 'Spa\CoursesController@getCourseIntakes')->name('spa.courseintakes.all');
        Route::post('spa/courses/intakes/bulk', 'Spa\CoursesController@bulkAddIntakes')->name('spa.intakes.bulkadd');
        Route::post('spa/courses/intakes/update', 'Spa\CoursesController@updateIntakes')->name('spa.intakes.update');
        Route::post('spa/courses/intakes/updateall', 'Spa\CoursesController@updateCourseIntakes')->name('spa.update.courseintakes');

        /*
            routes for attendance module
            Author: Harish Chandra Kharel
            */
        /* routes for marking attendance */
        Route::get('spa/attendance/mark/{load?}', 'Spa\AttendanceViewController@markAttendanceMain')->name('spa.attendance.mark');
        Route::post('spa/attendance/mark', 'Spa\AttendanceViewController@saveMarkedAttendance')->name('spa.attendance.mark.save');
        Route::get('spa/attendance/batch/{id}/students', 'Spa\AttendanceViewController@getBatchStudents')->name('spa.attendance.getbatchstudents');
        /* routes for attendance warnings */
        Route::get('spa/attendance/warnings', 'Spa\AttendanceViewController@getAttendanceWarningGrid')->name('spa.attendance.warnings');
        /* routes for attendance warnings */
        Route::get('spa/attendance/warnings/templates/{type}', 'Spa\AttendanceViewController@getWarningTemplates')->name('spa.attendance.warnings.templates');
        /* routes to send warning letters students */
        Route::post('spa/attendance/warnings/send', 'Spa\AttendanceViewController@sendLetterToStudentActions')->name('spa.attendance.warnings.send');
        /* routes for attendance warnings */
        Route::get('spa/attendance/warnings/logs/{batch}/{student}', 'Spa\AttendanceViewController@getStudentWarningLogs')->name('spa.attendance.getwarninglogs');

        /* routes for marking attendance for teacher */
        Route::get('spa/attendance/trainer', 'Spa\AttendanceViewController@trainerAttendanceMain')->name('spa.attendance.trainer');
        Route::get('spa/attendance/trainer/{batch}', 'Spa\AttendanceViewController@trainerAttendanceBatch')->name('spa.attendance.trainerbatch');
        /*
            routes for attendance module ends here
            */
        /* routes for reports */
        /* Route for attendance reports by college */
        Route::get('spa/reports/attendance', 'Spa\AttendanceReportController@reportCollege')->name('spa.reports.attendance.college');
        /* Route for attendance reports by batch */
        Route::get('spa/reports/attendance/batch', 'Spa\AttendanceReportController@reportBatch')->name('spa.reports.attendance.batch');
        /* routes for attendance warnings reports */
        Route::get('spa/reports/warnings', 'Spa\AttendanceReportController@getWarningLogs')->name('spa.reports.warnings');

        /* routes for course templates */
        /* created by Harish Chandra Kharel */
        Route::post('spa/courses/templates', 'Spa\CourseTemplateController@saveCourseTemplate')->name('spa.coursetemplates.save');
        Route::get('spa/courses/templates', 'Spa\CourseTemplateController@index')->name('spa.coursetemplates.list');
        Route::get('spa/courses/template/{id}/detail', 'Spa\CourseTemplateController@getTemplateDetails')->name('spa.coursetemplates.details');
        Route::get('spa/courses/template/{id}/courseunits', 'Spa\CourseTemplateController@getTemplateUnitsByCourse')->name('spa.coursetemplates.getunits');
        Route::delete('spa/courses/template/delete/{id}', 'Spa\CourseTemplateController@deleteCourseTemplate')->name('spa.coursetemplates.destroy');

        /*
            Routes for competencies
            */
        Route::get('spa/assessments', 'Spa\AssessmentsController@index')->name('spa.assessments.index');
        Route::get('spa/assessments/entry', 'Spa\AssessmentsController@entryIndex')->name('spa.assessments.entry');
        Route::get('spa/assessments/mark/{id}', 'Spa\AssessmentsController@mark')->name('spa.assessments.mark');
        Route::post('spa/assessments/mark', 'Spa\AssessmentsController@saveMarking')->name('spa.assessments.savemarking');
        Route::post('spa/assessments/mark/bulk', 'Spa\AssessmentsController@saveMarkingBulk')->name('spa.assessments.savebulkmarking');

        Route::get('spa/assessments/edit/{id}', 'Spa\AssessmentsController@getAssessmentEditData')->name('spa.assessments.edit');
        Route::get('spa/assessment/edit/{id}', 'Spa\AssessmentsController@getAssignedAssessmentEditData')->name('spa.assignedassessment.edit');

        Route::post('spa/assessments/save', 'Spa\AssessmentsController@saveAssessment')->name('spa.assessments.save');
        Route::post('spa/assessments/assign', 'Spa\AssessmentsController@assignSavedAssessment')->name('spa.assessments.assignsaved');

        Route::get('spa/assessments/assign/{id}', 'Spa\AssessmentsController@getAssignedData')->name('spa.assessments.assign');

        Route::get('spa/assessments/transer/{id}', 'Spa\AssessmentsController@getAssessmentTransferData')->name('spa.assessments.preparetransferdata');
        Route::post('spa/assessments/transer', 'Spa\AssessmentsController@transferResults')->name('spa.assessments.transfer');

        /**
         * Routes for Competency
         * scaffolding routes
         */
        Route::inertia('spa/assessment/questions', 'competencyscaffold/AssessmentQuestions');
        Route::inertia('spa/assessment/entry', 'competencyscaffold/AssessmentEntry');
        Route::inertia('spa/assessment/entry/class-needs-linking', 'competencyscaffold/ClassNeedsLinking');
        Route::inertia('spa/assessment/entry/{id}', 'competencyscaffold/AssessmentDetail');
        Route::inertia('spa/timetable', 'timetablescaffold/Main');
        Route::inertia('spa/timetable/add-students', 'timetablescaffold/AddStudents');

        // Miscellaneous Document
        Route::get('spa/miscellaneous/document', 'Spa\DocumentController@getDocument')->name('spa.miscellaneous.document');
        Route::post('spa/miscellaneous/create-folder', 'Spa\DocumentController@createFolder')->name('spa.miscellaneous.createfolder');
        Route::post('spa/miscellaneous/upload-documents', 'Spa\DocumentController@uploadDocuments')->name('spa.miscellaneous.uploadDocuments');
        Route::put('spa/miscellaneous/rename', 'Spa\DocumentController@renameFileFolder')->name('spa.miscellaneous.renameFileFolder');
        Route::post('spa/miscellaneous/delete', 'Spa\DocumentController@deleteDocument')->name('spa.miscellaneous.deleteDocument');
        Route::post('spa/miscellaneous/move', 'Spa\DocumentController@moveDocument')->name('spa.miscellaneous.moveDocument');
        Route::post('spa/miscellaneous/bookmark', 'Spa\DocumentController@bookmarkDocument')->name('spa.miscellaneous.bookmarkDocument');
        Route::post('spa/miscellaneous/search', 'Spa\DocumentController@searchDocument')->name('spa.miscellaneous.searchDocument');
        Route::get('spa/miscellaneous/downloadFile/{id}', 'Spa\DocumentController@downloadFile')->name('spa.miscellaneous.downloadFile');

        // Course Document
        Route::get('spa/course/document', 'Spa\DocumentController@getDocument')->name('spa.course.document');
        Route::post('spa/course/create-folder', 'Spa\DocumentController@createFolder')->name('spa.course.createfolder');
        Route::post('spa/course/upload-documents', 'Spa\DocumentController@uploadDocuments')->name('spa.course.uploadDocuments');
        Route::put('spa/course/rename', 'Spa\DocumentController@renameFileFolder')->name('spa.course.renameFileFolder');
        Route::post('spa/course/delete', 'Spa\DocumentController@deleteDocument')->name('spa.course.deleteDocument');
        Route::post('spa/course/move', 'Spa\DocumentController@moveDocument')->name('spa.course.moveDocument');
        Route::post('spa/course/bookmark', 'Spa\DocumentController@bookmarkDocument')->name('spa.course.bookmarkDocument');
        Route::post('spa/course/search', 'Spa\DocumentController@searchDocument')->name('spa.course.searchDocument');
        Route::get('spa/course/downloadFile/{id}', 'Spa\DocumentController@downloadFile')->name('spa.course.downloadFile');
        Route::post('spa/course/on-change-permission', 'Spa\DocumentController@onChangePermission')->name('spa.course.onChangePermission');

        // Subject Document
        Route::get('spa/subject/document', 'Spa\DocumentController@getDocument')->name('spa.subject.document');
        Route::post('spa/subject/create-folder', 'Spa\DocumentController@createFolder')->name('spa.subject.createfolder');
        Route::post('spa/subject/upload-documents', 'Spa\DocumentController@uploadDocuments')->name('spa.subject.uploadDocuments');
        Route::put('spa/subject/rename', 'Spa\DocumentController@renameFileFolder')->name('spa.subject.renameFileFolder');
        Route::post('spa/subject/delete', 'Spa\DocumentController@deleteDocument')->name('spa.subject.deleteDocument');
        Route::post('spa/subject/move', 'Spa\DocumentController@moveDocument')->name('spa.subject.moveDocument');
        Route::post('spa/subject/bookmark', 'Spa\DocumentController@bookmarkDocument')->name('spa.subject.bookmarkDocument');
        Route::post('spa/subject/search', 'Spa\DocumentController@searchDocument')->name('spa.subject.searchDocument');
        Route::get('spa/subject/downloadFile/{id}', 'Spa\DocumentController@downloadFile')->name('spa.subject.downloadFile');
        Route::post('spa/subject/on-change-permission', 'Spa\DocumentController@onChangePermission')->name('spa.subject.onChangePermission');

        // Admin Agent List Routes
        Route::group(['prefix' => 'spa/admin-agent'], function () {
            Route::inertia('agency-payment', 'admin-agent/AgencyPayment')->name('spa.admin-agent.agency-payment');
            Route::inertia('agency-payment/1', 'admin-agent/AgencyPaymentDetail')->name('spa.admin-agent.agencyPaymentDetail');
            Route::get('agency-mailing', 'Spa\Admin\AgentController@agencyMailing')->name('spa.admin-agent.agencyMailing');
            Route::get('add-agency', 'Spa\Admin\AgentController@index')->name('spa.admin.add-agency');
        });

        Route::get('spa/tcsi/credit-provider-code', 'Spa\Admin\CommonController@index')->name('spa.tcsi.credit-provider-code');
        Route::post('spa/tcsi/save-credit-provider-code', 'Spa\Admin\CommonController@save')->name('spa.tcsi.save-credit-provider-code');
        Route::post('spa/tcsi/delete-credit-provider-code', 'Spa\Admin\CommonController@destroy')->name('spa.tcsi-delete-credit-provider-code');

        // User Management Routes
        Route::group(['prefix' => 'spa/user'], function () {
            Route::inertia('manage-roles', 'user-management/ManageRoles')->name('spa.user-management.manage-roles');
            Route::inertia('staff-application', 'user-management/StaffApplication')->name('spa.user-management.staff-application');
        });

        // Organization Setup Pages - Legacy to Spa
        // Student Account steup
        Route::get('spa/bulk-update-result', 'Spa\Settings\BulkUpdateController@updateUnitOutcome')->name('spa.bulk-update-result');
        Route::get('spa/update-unit-outcome', 'Spa\Settings\BulkUpdateController@resultAvetmissInfo')->name('spa.update-unit-outcome');
        Route::get('spa/student-account-setup', 'Spa\Settings\StudentAccountSetupController@studentAccountSetup')->name('spa.student-account-setup');
        Route::post('spa/student-account-setup', 'Spa\Settings\StudentAccountSetupController@ajaxAction')->name('spa.student-account-setup-post');
        Route::post('spa/student-account-setup-add', 'Spa\Settings\StudentAccountSetupController@addAccountSetup')->name('spa.student-account-setup-add');
        Route::post('spa/student-account-setup/{id}', 'Spa\Settings\StudentAccountSetupController@editAccountSetup')->name('spa.student-account-setup-edit');
        Route::delete('spa/student-account-setup/{id}', 'Spa\Settings\StudentAccountSetupController@deletePaymentLedgerInfo')->name('spa.student-account-setup-destroy');
        Route::get('spa/student-ledger-account', 'Spa\Settings\StudentAccountSetupController@addLedgerAccountInfo')->name('spa.student-ledger-account');
        Route::post('spa/student-ledger-account', 'Spa\Settings\StudentAccountSetupController@addLedgerAccountInfo')->name('spa.student-ledger-account-post');
        Route::post('spa/student-ledger-account/save/{id}', 'Spa\Settings\StudentAccountSetupController@editLedgerAccount')->name('spa.student-ledger-account-edit');
        Route::delete('spa/student-ledger-account/{id}', 'Spa\Settings\StudentAccountSetupController@deleteLedgerAccountInfo')->name('spa.student-ledger-account-destroy');
        Route::get('spa/payment-mode-info', 'Spa\Settings\StudentAccountSetupController@addPaymentModeInfo')->name('spa.payment-mode-info');
        Route::post('spa/payment-mode-info', 'Spa\Settings\StudentAccountSetupController@addPaymentSetupMode')->name('spa.payment-mode-info-post');
        Route::delete('spa/payment-mode-info/{id}', 'Spa\Settings\StudentAccountSetupController@deletePaymentModeInfo')->name('spa.payment-mode-info-destroy');
        Route::post('spa/edit-payment-mode/{id}', 'Spa\Settings\StudentAccountSetupController@editPaymentMode')->name('spa.edit-payment-mode');
        Route::get('spa/define-level-activity', 'Spa\Settings\StudentAccountSetupController@defineLevelActivity')->name('spa.define-level-activity');
        Route::post('spa/define-level-activity', 'Spa\Settings\StudentAccountSetupController@ajaxAction')->name('spa.define-level-activity-post');
        Route::post('spa/define-level-activity-save', 'Spa\Settings\StudentAccountSetupController@addDefineLevelActivity')->name('spa.define-level-activity-add');
        Route::delete('spa/define-level-activity/{id}', 'Spa\Settings\StudentAccountSetupController@deleteActivityLevel')->name('spa.define-level-activity-destroy');
        Route::get('spa/setup-staff-level', 'Spa\Settings\StudentAccountSetupController@setupStaffLevel')->name('spa.setup-staff-level');
        Route::post('spa/setup-staff-level', 'Spa\Settings\StudentAccountSetupController@ajaxAction')->name('spa.setup-staff-level-post');
        // End Student Account

        // update status
        Route::get('spa/bulk-update-student-course', 'Spa\Settings\BulkUpdateController@updateStudentCourseInfo')->name('spa.bulk-update-student-course');
        Route::post('spa/bulk-update-student-course', 'Spa\Settings\BulkUpdateController@ajaxAction')->name('spa.bulk-update-student-course-post');
        Route::post('spa/bulk-update-student-course/save', 'Spa\Settings\BulkUpdateController@ajaxAction')->name('spa.bulk-update-student-course-save');
        Route::get('spa/bulk-update-course-template', 'Spa\Settings\BulkUpdateController@updateCourseTemplateInfo')->name('spa.bulk-update-course-template');
        Route::post('spa/bulk-update-course-template/save', 'Spa\Settings\BulkUpdateController@ajaxAction')->name('spa.bulk-update-course-template-save');
        // end update status

        // data reporting
        Route::get('spa/prisms-data-validation', 'Spa\Settings\ValidatePrismsController@validatePrisms')->name('spa.prisms-data-validation');
        Route::get('spa/student-avet-miss-data-exports', 'Spa\Settings\AvetMissDataExportController@StudentAvetDataExports')->name('spa.student-avet-miss-data-exports');
        Route::post('spa/student-avet-miss-data-exports', 'Spa\Settings\AvetMissDataExportController@ajaxAction')->name('spa.student-avet-miss-data-exports-post');
        Route::post('spa/student-avet-miss-data-exports/download', 'Spa\Settings\AvetMissDataExportController@generate_excel')->name('spa.exports-to-excels');
        Route::get('spa/nvr-report-data-extraction', 'Spa\Settings\NvrReportController@NvrReportDataExtraction')->name('spa.nvr-report-data-extraction');
        Route::post('spa/nvr-report-data-extraction', 'Spa\Settings\NvrReportController@ajaxAction')->name('spa.nvr-report-data-extraction-post');
        Route::get('spa/cqr-report-data-extraction', 'Spa\Settings\CqrReportController@CqrReportDataExtraction')->name('spa.cqr-report-data-extraction');
        Route::post('spa/cqr-report-data-extraction', 'Spa\Settings\CqrReportController@ajaxAction')->name('spa.cqr-report-data-extraction-post');
        // end data reporting

        // survey management
        Route::get('spa/survey-manager-question-management', 'Spa\Settings\SurveyManagementController@questionManagementInfo')->name('spa.survey-management');
        Route::post('spa/survey-manager-question-management', 'Spa\Settings\SurveyManagementController@ajaxAction')->name('spa.survey-management-post');
        Route::get('spa/survey-manager-activation', 'Spa\Settings\SurveyManagementController@activationInfo')->name('spa.survey-manager-activation');
        Route::post('spa/survey-manager-activation', 'Spa\Settings\SurveyManagementController@ajaxAction')->name('spa.survey-manager-activation-post');
        // Route::post('spa/survey-manager-activation-post/semster', 'Frontend\SemesterController@ajaxAction')->name('spa.semester');
        // Route::post('spa/survey-manager-activation-post/semster', 'Frontend\SemesterController@ajaxAction')->name('spa.semester');
        Route::post('spa/survey-manager-activation/{id}', 'Spa\Settings\SurveyManagementController@editSurveyActivation')->name('spa.edit-activation');
        Route::delete('spa/survey-manager-activation/{id}', 'Spa\Settings\SurveyManagementController@deleteSurveyActivation')->name('spa.delete-activation');
        Route::get('spa/survey-result-not-send-request', 'Spa\Settings\SurveyResultController@notSendRequestInfo')->name('spa.survey-result-not-send-request');
        Route::post('spa/survey-result/ajaxAction', 'Spa\Settings\SurveyResultController@ajaxAction')->name('spa.survey-result-ajaxAction');
        Route::get('spa/survey-result-send-request', 'Spa\Settings\SurveyManagementController@sendRequestInfo')->name('spa.survey-result-send-request');
        // end survey management

        // letter template
        // Route::get('spa/pdf-template-list', 'Spa\Settings\PdfTemplateController@pdfTemplateList')->name('spa.pdf-template-list');
        // Route::post('spa/pdf-template-list', 'Spa\Settings\PdfTemplateController@ajaxAction')->name('spa.pdf-template-list-post');
        // Route::post('spa/pdf-template-list', 'Spa\Settings\PdfTemplateController@ajaxAction')->name('spa.pdf-template-list-post');
        // Route::post('spa/edit-pdf-template-list/{id}', 'Spa\Settings\PdfTemplateController@editPdfTemplate')->name('spa.edit-pdf-template');
        // end letter template

        // manage calendar
        Route::get('spa/manage-semester', 'Spa\Settings\SemesterController@semesterManage')->name('spa.manage-semester');
        Route::post('spa/manage-semester', 'Spa\Settings\SemesterController@ajaxAction')->name('spa.manage-semester-post');
        Route::post('spa/manage-semester/add', 'Spa\Settings\SemesterController@addSemester')->name('spa.manage-semester-add');
        Route::post('spa/edit-manage-semester/{id}', 'Spa\Settings\SemesterController@ajaxAction')->name('spa.manage-semester-edit');
        Route::post('spa/delete-semester/{id}', 'Spa\Settings\SemesterController@deleteSemester')->name('spa.delete-semester');
        Route::get('spa/semester-division', 'Spa\Settings\SemesterController@semesterDivision')->name('spa.semester-division');
        Route::post('spa/semester-division', 'Spa\Settings\SemesterController@ajaxAction')->name('spa.semester-division-post');
        Route::post('spa/semester-division/add', 'Spa\Settings\SemesterController@semesterDivision')->name('spa.semester-division-add');
        Route::post('spa/semester-division/save', 'Spa\Settings\SemesterController@ajaxAction');
        Route::delete('spa/delete-semester-division/{id}', 'Spa\Settings\SemesterController@deleteSemesterDivision')->name('spa.delete-semester-division');
        Route::get('spa/course-calendar', 'Spa\Settings\SemesterController@courseCalendar')->name('spa.course-calendar');
        Route::post('spa/add-course-calendar', 'Spa\Settings\SemesterController@addCourseCalendar')->name('spa.course-calendar-add');
        Route::post('spa/semester/ajaxAction', 'Spa\Settings\SemesterController@ajaxAction')->name('spa.course-calendar-post');
        Route::delete('spa/delete-course-calendar/{id}', 'Spa\Settings\SemesterController@deleteCourseCalendar')->name('spa.delete-course-calendar');
        // end manage calendar

        // start add contract code
        Route::get('spa/add-contract-code', 'Spa\Settings\ContractController@addContractCode')->name('spa.add-contract-code');
        Route::post('spa/add-contract-code', 'Spa\Settings\ContractController@addContractCode');
        // end add contract code

        // start profile
        Route::get('spa/profile', 'Spa\Settings\UsersController@profile')->name('spa.profile');
        Route::post('spa/profile', 'Spa\Settings\UsersController@profile');
        Route::get('spa/change-password', 'Spa\Settings\UsersController@passwordChange')->name('spa.change-password');
        Route::post('spa/change-password', 'Spa\Settings\UsersController@passwordChange');
        Route::inertia('spa/security', 'onboarding/Security')->name('spa.security');
        Route::post('spa/logout', 'Spa\Settings\UsersController@logout')->name('spa.logout');
        // end profile

        // setup service
        Route::get('spa/services-setup', 'Spa\Settings\SetupServicesController@managerServicesSetup')->name('spa.services-setup');
        Route::post('spa/services-setup', 'Spa\Settings\SetupServicesController@ajaxAction');
        Route::post('spa/services-setup-add', 'Spa\Settings\SetupServicesController@addServicesSetup')->name('spa.services-setup-add');
        Route::delete('spa/services-setup/{id}', 'Spa\Settings\SetupServicesController@ajaxAction');
        // end setup service

        // update service
        Route::get('spa/service-request-allocation', 'Spa\Settings\ServiceRequestAllocationController@serviceProviderAllocation')->name('spa.service-request-allocation');
        Route::post('spa/service-request-allocation', 'Spa\Settings\ServiceRequestAllocationController@ajaxAction');
        // end update service

        // service provider
        Route::get('spa/provider-setup', 'Spa\Settings\SetupServicesController@providerSetup')->name('spa.provider-setup');
        Route::post('spa/provider-setup', 'Spa\Settings\SetupServicesController@addServiceProvider');
        Route::post('spa/provider-setup-edit/{id}', 'Spa\Settings\SetupServicesController@editServiceProvider')->name('spa.provider-setup-edit');
        // end service provider

        // provider payment
        Route::get('spa/provider-payment-v2', 'Spa\Settings\ProviderPaymentController@providerPaymentV2')->name('spa.provider-payment-v2');
        Route::post('spa/provider-payment-v2', 'Spa\Settings\ProviderPaymentController@ajaxAction');
        // end provider payment

        // Bank Reconciliation
        Route::get('spa/bank-reconciliation', 'Spa\Settings\BankReconciliationController@bankReconciliation')->name('spa.bank-reconciliation');
        Route::post('spa/bank-reconciliation/all', 'Spa\Settings\BankReconciliationController@ajaxAction')->name('spa.bank-reconciation-post');
        Route::delete('spa/bank-reconciliation/{id}', 'Spa\Settings\BankReconciliationController@deleteBankReconciliation')->name('spa.bank-reconciation.destroy');
        Route::put('spa/bank-reconciliation', 'Spa\Settings\BankReconciliationController@editBankReconciliation')->name('spa.bank-reconciliation.edit');
        Route::post('spa/bank-reconciliation', 'Spa\Settings\BankReconciliationController@addBankReconciliation')->name('spa.bank-reconciliation.add');

        // Staff Evaluation
        Route::get('spa/admin-evaluation', 'Spa\Settings\EvaluationController@viewEvaluationInfo')->name('spa.admin-evaluation');
        Route::post('spa/admin-evaluation', 'Spa\Settings\EvaluationController@ajaxAction');
        // end Staff Evaluation

        // Leave Info
        Route::get('spa/view-leave-list', 'Spa\Settings\LeaveController@viewLeaveList')->name('spa.view-leave-list');
        Route::get('spa/add-leave-info', 'Spa\Settings\LeaveController@addLeaveInfo')->name('spa.add-leave-info');
        Route::post('spa/add-leave-info', 'Spa\Settings\LeaveController@addLeaveInfoNew');
        Route::delete('spa/delete-leave-info/{id}', 'Spa\Settings\LeaveController@deleteLeaveInfo')->name('spa.delete-leave-info');
        // end Leave Info

        // payment process timesheet
        Route::get('spa/process-timesheet', 'Spa\Settings\StaffTimesheetController@processTimesheet')->name('spa.process-timesheet');
        Route::post('spa/process-timesheet', 'Spa\Settings\StaffTimesheetController@ajaxAction');
        // end process timesheet

        // Staff invoice timesheet
        Route::get('spa/staff-invoice/{year}/{period_id}', 'Spa\Settings\StaffTimesheetController@staffInvoice')->name('spa.staff-invoice');
        Route::post('spa/process-timesheet', 'Spa\Settings\StaffTimesheetController@ajaxAction');
        // end

        // Timesheet Payperiod
        // Route::get('spa/generate-pay-period-old', 'Spa\Settings\TimesheetController@generatePayPeriodInfo')->name('spa.generate-pay-period');
        // Route::post('spa/generate-pay-period-old', 'Spa\Settings\TimesheetController@ajaxAction');
        // Route::post('spa/generate-pay-period-add-old', 'Spa\Settings\TimesheetController@addTimeSheet')->name('spa.generate-pay-period-add');
        // Route::delete('spa/delete-generated-pay-period-old/{id}', 'Spa\Settings\TimesheetController@deleteGeneratedPayPeriodInfo')->name('spa.delete-generated-pay-period');
        // end

        Route::inertia('spa/certificate/attributes', 'certificate-builder/CertificateAttributes')->name('spa.certificate.attributes');
        Route::get('spa/certificate-builder', 'Spa\CertificateTemplateController@index')->name('spa.certificate-builder');
        Route::get('spa/generte-certificate', 'Spa\CertificateGenerateController@generteCertificate')->name('spa.generte-certificate');
        Route::get('spa/certificate/templates', 'Spa\CertificateTemplateController@templateList')->name('spa.certificate.templates');
        Route::put('spa/certificates/update-template/{template}', 'Spa\CertificateTemplateController@update')->name('spa.certificate-builder.update');
        Route::post('spa/certificates/save-template', 'Spa\CertificateTemplateController@store')->name('spa.certificate-builder.store');
        Route::post('spa/certificates/get-preview-url', 'Spa\CertificateTemplateController@getPreviewUrl')->name('spa.certificate-preview.url');
        Route::delete('spa/certificates/delete-template/{template}', 'Spa\CertificateTemplateController@delete')->name('spa.certificate-builder.delete-template');

        // Result Import Routes
        Route::get('spa/result-import', 'Spa\ResultImportController@index')->name('spa.result-import.index');
        Route::get('spa/result-import/list', 'Spa\ResultImportController@getImports')->name('spa.result-import.list');
        Route::post('spa/result-import/upload', 'Spa\ResultImportController@store')->name('spa.result-import.store');
        Route::get('spa/result-import/sample/{type}', 'Spa\ResultImportController@downloadSample')->name('spa.result-import.sample');
        Route::get('spa/result-import/{id}', 'Spa\ResultImportController@show')->name('spa.result-import.show');
        Route::put('spa/result-import/{id}', 'Spa\ResultImportController@update')->name('spa.result-import.update');
        Route::post('spa/result-import/{id}/resync', 'Spa\ResultImportController@resync')->name('spa.result-import.resync');
        Route::post('spa/result-import/bulk-delete', 'Spa\ResultImportController@bulkDestroy')->name('spa.result-import.bulk-destroy');
        Route::delete('spa/result-import/{id}', 'Spa\ResultImportController@destroy')->name('spa.result-import.destroy');
    });
    /* search for students */
    Route::post('spa/search/students', [CommonService::class, 'searchStudents'])->name('spa.search.students');
    /*
        Route to load the filter data
        */
    Route::get('spa/filters/load', [CommonService::class, 'loadRequestedFilters'])->name('spa.filters.loadfilters');
    Route::get('spa/filters/subjects', [CommonService::class, 'loadCourseSubjects'])->name('spa.filters.loadsubjects');
    Route::get('spa/filters/courses', [CommonService::class, 'loadCourses'])->name('spa.filters.loadcourses');

    /*
        Route to save/load the filter data for users
        */
    Route::post('spa/filter/save', 'App\Http\Controllers\v2\api\FilterController@saveFilter')->name('v2.api.savefilter');
    Route::post('spa/filters/get', 'App\Http\Controllers\v2\api\FilterController@getFilters')->name('v2.api.getfilters');
    Route::post('spa/filters/delete', 'App\Http\Controllers\v2\api\FilterController@deleteFilter')->name('v2.api.deletefilter');

    /**
     * Student Portal Routes
     */
    Route::group(['prefix' => 'spa/students', 'middleware' => [
        'student',
        PreventAccessFromCentralDomains::class,
    ]], function () {

        // Dashboard
        Route::get('dashboard', 'Spa\Student\DashboardController@studentDashboard')->name('spa.studentportal.dashboard');

        // Document
        // Route::get('document', 'Spa\Student\CourseMaterialController@viewStudentDocument')->name('spa.studentportal.documents');

        // Attendance
        Route::get('weekly-attendance-detail', 'Spa\Student\AttendanceController@viewWeeklyAttendanceInfo')->name('spa.studentportal.attendance.weekly');
        Route::get('overall-attendance-detail', 'Spa\Student\AttendanceController@viewOverallAttendanceInfo')->name('spa.studentportal.attendance.overall');
        Route::get('attendance/ajaxAction', 'Spa\Student\AttendanceController@ajaxAction')->name('spa.studentportal.attendance.ajax');

        // OSHC
        Route::get('oshc-info', 'Spa\Student\OshcInfoController@oshcInfo')->name('spa.studentportal.oshcinfo');

        // Results
        Route::get('results', 'Spa\Student\ResultController@viewAcademicResult')->name('spa.studentportal.results');

        // Timetable
        Route::get('timetable', 'Spa\Student\TimetableController@studentTimetable')->name('spa.studentportal.timetable');

        // Payment
        Route::get('payment-information', 'Spa\Student\StudentPaymentInformationController@StudentPaymentInformation')->name('spa.studentportal.paymentinfo');

        // Warning Logs
        Route::get('warning-log', 'Spa\Student\WarningLogController@warningLog')->name('spa.studentportal.warning-log');

        // Communication
        Route::get('email-feedback', 'Spa\Student\CommunicationController@sendEmailFeedback')->name('spa.studentportal.email-feedback');
        Route::post('email-feedback', 'Spa\Student\CommunicationController@sendEmailFeedback')->name('spa.studentportal.send-email-feedback');
        Route::get('email-to-trainer', 'Spa\Student\CommunicationController@sendEmailToTrainer')->name('spa.studentportal.email-to-trainer');
        Route::get('email-communication-log', 'Spa\Student\CommunicationController@studentCommunicationLogList')->name('spa.studentportal.email-communication-log');

        // Evaluation
        Route::get('evaluation-view', 'Spa\Student\EvaluationController@viewEvaluationInfo')->name('spa.studentportal.evaluation-view');
        Route::post('submit-evaluation', 'Spa\Student\EvaluationController@submitEvaluation')->name('spa.studentportal.submit-evaluation');

        // ELearning
        Route::get('elearning', 'Spa\Student\ElearningLinkController@elearningLinkList')->name('spa.studentportal.elearning');

        Route::post('send_email_to_trainer', 'Spa\Student\CommunicationController@sendEmailToTrainerPost')->name('spa.studentportal.send_email_to_trainer');
        Route::post('send-post-email-feedback', 'Spa\Student\CommunicationController@sendEmailFeedbackPost')->name('spa.studentportal.send-post-email-feedback');
        Route::post('get_subject_list_from_course', 'Spa\Student\CommunicationController@ajaxAction')->name('spa.studentportal.ajaxAction');

        Route::post('update-last-password-change-date', 'Spa\Student\DashboardController@updateUserLastPasswordDate')->name('spa.studentportal.update-last-password-change-date');
        Route::post('update-last-address-change-date', 'Spa\Student\DashboardController@updateUserLastAddressDate')->name('spa.studentportal.update-last-address-change-date');

        // Document
        Route::get('/miscellaneous/document', 'Spa\Student\DocumentController@getDocument')->name('spa.studentportal.document');
        Route::post('/miscellaneous/create-folder', 'Spa\Student\DocumentController@createFolder')->name('spa.studentportal.createfolder');
        Route::post('/miscellaneous/upload-documents', 'Spa\Student\DocumentController@uploadDocuments')->name('spa.studentportal.uploadDocuments');
        Route::put('/miscellaneous/rename', 'Spa\Student\DocumentController@renameFileFolder')->name('spa.studentportal.renameFileFolder');
        Route::post('/miscellaneous/delete', 'Spa\Student\DocumentController@deleteDocument')->name('spa.studentportal.deleteDocument');
        Route::post('/miscellaneous/move', 'Spa\Student\DocumentController@moveDocument')->name('spa.studentportal.moveDocument');
        Route::post('/miscellaneous/bookmark', 'Spa\Student\DocumentController@bookmarkDocument')->name('spa.studentportal.bookmarkDocument');
        Route::post('/miscellaneous/search', 'Spa\Student\DocumentController@searchDocument')->name('spa.studentportal.searchDocument');
        Route::get('/miscellaneous/downloadFile/{id}', 'Spa\Student\DocumentController@downloadFile')->name('spa.studentportal.downloadFile');

        Route::get('/course/document', 'Spa\Student\DocumentController@getDocument')->name('spa.studentportal.courseDocument');
        Route::post('/course/search', 'Spa\Student\DocumentController@searchDocument')->name('spa.studentportal.searchCourseDocument');
        Route::post('/course/bookmark', 'Spa\Student\DocumentController@bookmarkDocument')->name('spa.studentportal.bookmarkCourseDocument');
        Route::get('/course/downloadFile/{id}', 'Spa\Student\DocumentController@downloadFile')->name('spa.studentportal.downloadCourseFile');

        Route::get('/subject/document', 'Spa\Student\DocumentController@getDocument')->name('spa.studentportal.subjectDocument');
        Route::post('/subject/search', 'Spa\Student\DocumentController@searchDocument')->name('spa.studentportal.searchSubjectDocument');
        Route::post('/subject/bookmark', 'Spa\Student\DocumentController@bookmarkDocument')->name('spa.studentportal.bookmarkSubjectDocument');
        Route::get('/subject/downloadFile/{id}', 'Spa\Student\DocumentController@downloadFile')->name('spa.studentportal.downloadSubjectFile');

        Route::get('generate-schedule-invoice-pdf', 'Spa\Student\StudentPaymentInformationController@generateStudentScheduleInvoicePdf')->name('spa.studentportal.download-schedule-invoice-pdf');
        Route::get('generate-student-tax-receipt-pdf', 'Spa\Student\StudentPaymentInformationController@generateStudentTaxReceiptPdf')->name('spa.studentportal.download-student-tax-receipt-pdf');

        // misslin
        Route::get('generate-miscellaneous-invoice-pdf', 'Spa\Student\StudentPaymentInformationController@miscellaneousPaymentInvoicePdf')->name('spa.studentportal.download-miscellaneous-invoice-pdf');
        Route::get('generate-miscellaneous-receipt-pdf', 'Spa\Student\StudentPaymentInformationController@miscellaneousPaymentReceiptPdf')->name('spa.studentportal.download-miscellaneous-receipt-pdf');

        // Addtional serivce
        Route::get('generate-additional-service-invoice-pdf', 'Spa\Student\StudentPaymentInformationController@generateInvoicesAdditionalPaymentServicePdf')->name('spa.studentportal.download-additional-service-invoice-pdf');
        Route::get('generate-additional-service-receipt-pdf', 'Spa\Student\StudentPaymentInformationController@generateReceiptAdditionalPaymentServicePdf')->name('spa.studentportal.download-additional-service-receipt-pdf');
    });

    /**
     * Agent Portal Routes
     */
    Route::group(['prefix' => 'spa/agent', 'middleware' => [
        'web',
        'mail',
        'agent',
        InitializeTenancyByDomain::class,
        PreventAccessFromCentralDomains::class,
    ]], function () {
        // Students
        Route::get('student-list', 'Spa\Agent\AgentStudentController@agentStudents')->name('spa.agent.studentList'); // Previously Your Student
        Route::get('requested-student', 'Spa\Agent\AgentStudentController@requestedStudent')->name('spa.agent.requestedStudent');
        Route::get('communication-archives', 'Spa\Agent\AgentStudentController@communicationArchive')->name('spa.agent.communicationArchives');
        Route::post('communication-archives/view', 'Spa\Agent\AgentStudentController@viewCommunicationArchive')->name('spa.agent.viewcommunicationArchives');
        Route::post('sentmail/view', 'Spa\Agent\AgentRequestsController@viewMailbox')->name('spa.agent.viewmailbox');
        Route::post('place-communication-archive-request', 'Spa\Agent\AgentStudentController@placeCommunicationArchiveRequest')->name('spa.agent.place-communication-archive-request');
        Route::get('offered-student', 'Spa\Agent\AgentStudentController@offeredStudent')->name('spa.agent.offeredStudent');

        Route::inertia('sentmail', 'agentportal/mailbox/Sentmail')->name('spa.agent.sentmail');

        Route::get('sentmail/inbox', 'Spa\Agent\AgentRequestsController@agentRequestsInbox')->name('spa.agent.inbox');
        Route::get('sentmail/outbox', 'Spa\Agent\AgentRequestsController@agentRequestsOutbox')->name('spa.agent.outbox');
        Route::post('place-request', 'Spa\Agent\AgentRequestsController@placeRequest')->name('spa.agent.placerequest');

        Route::get('staff-application', 'Spa\Agent\AgentStudentController@staffApplications')->name('spa.agent.staffApplications');

        Route::get('continue-application', 'Spa\Agent\AgentStudentController@continueApplication')->name('spa.agent.continueApplication');
        Route::post('delete-application', 'Spa\Agent\AgentStudentController@deleteApplication')->name('spa.agent.deleteApplication');
        Route::get('new-application', 'Spa\Agent\AgentStudentController@newApplication')->name('spa.agent.newApplication');
        Route::get('reject-application', 'Spa\Agent\AgentStudentController@rejectApplication')->name('spa.agent.rejectApplication');
        // scaffolding route for student-profile
        Route::get('student-profile/{id}', 'Spa\Agent\AgentStudentController@studentProfile')->name('spa.agent.student.profile.scaffolding');

        Route::get('add-view-agent-offer-communication/{id}/{offer_id}', 'Spa\Agent\AgentStudentController@offerCommunication')->name('spa.agent.offerCommunication');
        Route::post('save-agent-offer-communication', 'Spa\Agent\AgentStudentController@saveOfferCommunication')->name('spa.agent.save-agent-offer-communication');

        Route::post('delete-student-application', 'Spa\Agent\AgentStudentController@deleteStudentApplication')->name('spa.agent.delete_application');

        // Payment & Commission
        Route::get('commission', 'Spa\Agent\AgentPaymentController@agentCommission')->name('spa.agent.commission');
        Route::get('approved-comission', 'Spa\Agent\AgentPaymentController@getApprovedCommission')->name('spa.agent.approvedcommission');
        Route::get('paid-commission', 'Spa\Agent\AgentPaymentController@getPaidCommission')->name('spa.agent.paidcommision');

        // Report
        // Route::get('report', 'Spa\Agent\AgentReportController@report')->name('spa.agent.report');

        // Request
        Route::get('report', 'Spa\Agent\AgentReportsController@agentReports')->name('spa.agent.report');

        // Route::inertia('staff-list', 'agentportal/stafflist/StaffList')->name('spa.agent.staff-list');
        Route::get('staff-list', 'Spa\Agent\AgentStaffListController@agentStaffList')->name('spa.agent.staffList');
        Route::post('staff/invite', 'Spa\Agent\AgentStaffListController@sendEmailForInviteAgentStaff')->name('spa.agent.send-email-invite-for-staff');
        Route::post('staff/reinvite', 'Spa\Agent\AgentStaffListController@resendEmailForInviteAgentStaff')->name('spa.agent.reinvite-staff');
        Route::post('staff/save', 'Spa\Agent\AgentStaffListController@saveAgentStaffData')->name('spa.agent.save-agent-staff-data');
        Route::post('staff/delete', 'Spa\Agent\AgentStaffListController@deleteAgentStaffData')->name('spa.agent.delete-agent-staff-data');
        Route::post('staff/status', 'Spa\Agent\AgentStaffListController@updateStatusAgentStaffData')->name('spa.agent.update-status-agent-staff-data');

        Route::get('student/profile/{id}', 'Spa\Agent\AgentStudentController@studentProfile')->name('spa.agent.student.profile');

        Route::post('download-student-profile', 'Spa\Agent\AgentStudentController@studentProfileDownload')->name('spa.agent.student.profile-download');

        Route::inertia('offer/communication/{id}/{offer_id}', 'agentportal/student/CommunicationOffer')->name('spa.agent.offer.communication');
        // GTE Process
        Route::get('gte-dashboard/{id}', 'Spa\Agent\AgentGteProcessController@gteDashboard')->name('spa.agent.gteDashboard');
        // Route::inertia('profile', 'agentportal/agentprofile/AgentProfile');
        Route::get('profile', 'Spa\Agent\AgentProfileController@agentProfile')->name('spa.agent.agentprofile');
        Route::get('profile-staff', 'Spa\AgentStaff\AgentStaffProfileController@agentProfile')->name('spa.agent.agentstaffprofile');

        // Document
        Route::get('document', 'Spa\Agent\AgentDocumentController@getDocument')->name('spa.agent.document');
        Route::post('create-folder', 'Spa\Agent\AgentDocumentController@createFolder')->name('spa.agent.createfolder');
        Route::post('upload-documents', 'Spa\Agent\AgentDocumentController@uploadDocuments')->name('spa.agent.uploadDocuments');
        Route::put('rename', 'Spa\Agent\AgentDocumentController@renameFileFolder')->name('spa.agent.renameFileFolder');
        Route::post('delete', 'Spa\Agent\AgentDocumentController@deleteDocument')->name('spa.agent.deleteDocument');
        Route::post('move', 'Spa\Agent\AgentDocumentController@moveDocument')->name('spa.agent.moveDocument');
        Route::post('bookmark', 'Spa\Agent\AgentDocumentController@bookmarkDocument')->name('spa.agent.bookmarkDocument');
        Route::post('search', 'Spa\Agent\AgentDocumentController@searchDocument')->name('spa.agent.searchDocument');
        Route::get('downloadFile/{id}', 'Spa\Agent\AgentDocumentController@downloadFile')->name('spa.agent.downloadFile');
    });

    // for agent staff
    Route::group(['prefix' => 'spa/agentstaff', 'middleware' => [
        InitializeTenancyByDomain::class,
        PreventAccessFromCentralDomains::class,
    ]], function () {
        Route::get('student-online-application', 'Spa\AgentStaff\AgentStaffStudentController@agentStaffStudentIncompleteList')->name('spa.agentstaff.student-online-application');
        Route::get('student-online-completed-application', 'Spa\AgentStaff\AgentStaffStudentController@agentStaffStudentCompleteList')->name('spa.agentstaff.student-online-completed-application');
        Route::get('student/profile/{id}', 'Spa\AgentStaff\AgentStaffStudentController@studentProfile')->name('spa.agentstaff.student.profile');
        Route::get('add-view-agentstaff-offer-communication/{id}/{offer_id}', 'Spa\AgentStaff\AgentStaffStudentController@offerCommunication')->name('spa.agentstaff.offerCommunication');
        Route::post('save-agentstaff-offer-communication', 'Spa\AgentStaff\AgentStaffStudentController@saveOfferCommunication')->name('spa.agentstaff.save-agentstaff-offer-communication');

        Route::get('incompleted-application', 'Spa\AgentStaff\AgentStaffStudentController@inCompletedApplication')->name('spa.agentstaff.incompleted-application');

        Route::get('completed-application', 'Spa\AgentStaff\AgentStaffStudentController@completedApplication')->name('spa.agentstaff.completed-application');
        Route::get('create-student-application', 'Spa\AgentStaff\AgentStaffStudentController@createStudentApplication')->name('spa.agentstaff.create-student-application');
        Route::get('profile', 'Spa\AgentStaff\AgentStaffProfileController@agentProfile')->name('spa.agentstaff.profile');
        Route::post('update-profile-url', 'Spa\AgentStaff\AgentStaffProfileController@agentProfileUpload')->name('spa.agentstaff.profileupload');
        Route::post('update-agentstaff-data', 'Spa\AgentStaff\AgentStaffProfileController@updateAgentStaffData')->name('spa.agentstaff.update-agentstaff-data');
        // Route::get('completed-application', 'Spa\Agentstaff\AgentStaffStudentController@completedApplication')->name('spa.agentstaff.completed-application');
        // Route::get('create-student-application', 'Spa\Agentstaff\AgentStaffStudentController@createStudentApplication')->name('spa.agentstaff.create-student-application');
        // Route::get('profile', 'Spa\Agentstaff\AgentStaffProfileController@agentProfile')->name('spa.agentstaff.profile');
        // Route::post('profile', 'Spa\Agentstaff\AgentStaffProfileController@agentProfileUpload')->name('spa.agentstaff.profileupload');
    });

    Route::inertia('generate-report', 'teacher/reports/TrainerReport');

    Route::prefix('spa')
        ->middleware([
            InitializeTenancyByDomain::class,
            PreventAccessFromCentralDomains::class,
        ])
        ->group(function () {
            Route::inertia('usi-verifications', 'usi-verifications/IndexPage')->name('spa.usi-verifications');
            Route::inertia('generate-pay-period', 'pay-period/IndexPage')->name('spa.generate-pay-period');

            // Pdf Templates
            Route::inertia('pdf-templates', 'admin-settings/pdf-templates/PdfTemplatePage')->name('spa.pdf-templates');
            Route::inertia('register-improvement', 'continuous-improvement/RegisterImprovementPage')->name('spa.register-improvements');
        });
});
